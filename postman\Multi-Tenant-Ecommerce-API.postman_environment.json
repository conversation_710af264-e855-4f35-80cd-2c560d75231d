{"name": "Multi-Tenant-Ecommerce-API Environment", "values": [{"key": "baseUrl", "value": "http://localhost:4000", "type": "text", "enabled": true}, {"key": "accessToken", "value": "", "type": "text", "enabled": true}, {"key": "refreshToken", "value": "", "type": "text", "enabled": true}, {"key": "vendorId", "value": "", "type": "text", "enabled": true}, {"key": "customerId", "value": "", "type": "text", "enabled": true}, {"key": "categoryId", "value": "", "type": "text", "enabled": true}, {"key": "productId", "value": "", "type": "text", "enabled": true}, {"key": "orderId", "value": "", "type": "text", "enabled": true}, {"key": "subcategoryId", "value": "", "type": "text", "enabled": true}, {"key": "tenantId", "value": "", "type": "text", "enabled": true}, {"key": "searchQuery", "value": "laptop", "type": "text", "enabled": true}]}